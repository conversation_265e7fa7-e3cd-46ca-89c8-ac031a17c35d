# Hub Service Overview

## Purpose

The hub service is an internal platform at luca that provides a central
interface for employees to access data, run operations, and use AI tools.

## Main Features

### Chat Interface

An AI assistant that can:

- Understand business context
- Remember past conversations
- Perform multi-step actions using natural language

### Dashboard

A customizable view that shows:

- Business data and metrics
- User-defined widgets and settings

### Knowledge Base

A system for storing and retrieving internal documents and information using:

- AI-based semantic search
- Natural language question answering

### Analytics

Access to real-time reports and data summaries:

- Pulls from existing services
- Supports KPI tracking and automated updates

## Technical Design

### Dedicated Database

The hub has its own PostgreSQL database using Drizzle ORM. This setup:

- Keeps hub data separate
- Improves query performance
- Enables type safety and isolated development

### tRPC API Layer

tRPC connects the frontend and backend with:

- Shared types between client and server
- No need for REST or GraphQL boilerplate
- Safer and faster development

### Dual AI System

- **Frontend (Vercel AI SDK)**: Handles user chats, session state, and connects
  to hub functions
- **Backend (Python services)**: Handles search, ML models, and heavy data
  processing

## Integration

- Auth: Reuses existing OIDC/JWT setup
- Data: Pulls business data from existing services
- AI: Uses backend Python services for intensive AI tasks

### Data Separation

- Hub-specific data (e.g., chat logs) stays in the hub DB
- Core business data (e.g., orders) stays where it is
- AI tasks are split between frontend SDK and backend services

## Benefits

### For Users

- One interface for tools and data
- Natural language support
- Custom views and preferences

### For Developers

- Type-safe codebase
- Fewer bugs from mismatched data types
- Easier to maintain and extend

### For Operations

- Faster access to data
- Searchable internal knowledge
- Supports future AI tools without major changes

## Technical Stack

- **Next.js 15** with App Router and React Server Components
- **shadcn/ui** components with Tailwind CSS
- **TypeScript** for type safety
- **tRPC** for type-safe API operations
- **Drizzle ORM** with PostgreSQL database
- **Vercel AI SDK** for AI orchestration
- **Docker** containerization for development and production
- **ELB routing** integration at `/hub`
- **Internationalization** with React Intl (English/German)
- **Authentication** integration with existing OIDC system

## Development

### Local Development

```bash
# Install dependencies
yarn install

# Start development server
yarn dev
```

The service will be available at:

- Local: http://localhost:8080
- Through ELB: https://localhost/hub

### Environment Variables

For the AI Assistant feature, you'll need to set:

```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### Docker Development

```bash
# From the root of luca-web repository
yarn dev up hub
```

## Production

The service is configured for production deployment with:

- Standalone output mode for minimal Docker images
- Multi-stage Docker builds
- Non-root user for security
- Port 8080 for container networking

## Architecture

- **Base Path**: `/hub` for ELB routing
- **Port**: 8080 (internal container port)
- **External Port**: 8092 (mapped in docker-compose.local.yml)
- **Dependencies**: ELB for routing

## Integration

This service is fully integrated into the Luca infrastructure:

- Added to `workspace.json` for Nx workspace management
- Included in `scripts/yarnAll.sh` for dependency management
- Configured in all Docker Compose files
- ELB routing configured in `services/elb/nginx.conf`
