# Hub Service Usage Guide

## Overview

The hub service is a modern Next.js-based feature hub and central dashboard with
AI integration. It serves as a quality-of-life service that provides a clean,
modern interface for internal users to access various luca platform features.

**Key Technologies:**

- **Next.js 15** with App Router and React 19
- **Tailwind CSS v4** for utility-first styling
- **shadcn/ui** components based on Radix UI primitives
- **TanStack Query** for data fetching and caching
- **React Intl** for internationalization (English/German)
- **AI SDK** for AI-powered features
- **Next Themes** for dark/light mode support

## Getting Started

### Prerequisites

- Node.js 18+ and Yarn
- Basic understanding of React concepts
- Familiarity with TypeScript

### Development Setup

```bash
# Navigate to hub service
cd services/hub

# Install dependencies
yarn install

# Start development server
yarn dev
```

The service runs on `http://localhost:8080` in development mode.

## Architecture Overview

### Next.js App Router Structure

The hub service uses Next.js 15's App Router with a file-based routing system:

```
src/app/
├── layout.tsx              # Root layout with providers
├── page.tsx               # Landing page
├── globals.css            # Global styles
├── (logged-in)/           # Route group for authenticated users
│   ├── layout.tsx         # Authenticated layout with sidebar
│   ├── dashboard/         # Dashboard pages
│   ├── ai-assistant/      # AI chat interface
│   └── profile/           # User profile pages
└── api/                   # API routes
    └── chat/              # AI chat endpoints
```

**Key Concepts:**

- **Route Groups**: `(logged-in)` groups authenticated routes without affecting
  URL structure
- **Layouts**: Shared UI that persists across route changes
- **Server Components**: Default rendering mode for better performance
- **Client Components**: Marked with `'use client'` for interactivity

### Component Organization

```
src/components/
├── ui/                    # shadcn/ui reusable components
│   ├── button.tsx         # Button variants
│   ├── input.tsx          # Form inputs
│   └── ...               # Other UI primitives
├── providers/             # Context providers
│   ├── providers.tsx      # Root provider composition
│   ├── query-provider.tsx # TanStack Query setup
│   ├── theme-provider.tsx # Theme management
│   └── intl-provider.tsx  # Internationalization
├── auth/                  # Authentication components
├── ai/                    # AI-related components
└── [feature]/             # Feature-specific components
```

## Core Concepts

### 1. Server vs Client Components

**Server Components (Default):**

- Render on the server
- Can access databases and APIs directly
- Better performance and SEO
- Cannot use browser APIs or event handlers

**Client Components:**

- Marked with `'use client'`
- Render in the browser
- Can use hooks, event handlers, browser APIs
- Required for interactivity

```tsx
// Server Component (default)
export default function Dashboard() {
  return <div>Server-rendered content</div>;
}

// Client Component
('use client');
export default function InteractiveButton() {
  const [count, setCount] = useState(0);
  return <button onClick={() => setCount(count + 1)}>{count}</button>;
}
```

### 2. Layouts and Nested Layouts

Layouts provide shared UI that persists across route changes:

```tsx
// Root layout (app/layout.tsx)
export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}

// Authenticated layout (app/(logged-in)/layout.tsx)
export default function AuthenticatedLayout({ children }) {
  return (
    <div className="flex">
      <Sidebar />
      <main>{children}</main>
    </div>
  );
}
```

### 3. Styling with Tailwind CSS

The hub service uses Tailwind CSS v4 for utility-first styling:

```tsx
// Basic styling
<div className="bg-background text-foreground p-4 rounded-lg">
  Content
</div>

// Responsive design
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  Cards
</div>

// Conditional classes with cn() utility
import { cn } from '@/lib/utils';

<button
  className={cn(
    'px-4 py-2 rounded',
    isActive && 'bg-primary text-primary-foreground',
    className
  )}
>
  Button
</button>
```

### 4. shadcn/ui Components

Pre-built, accessible components based on Radix UI:

```tsx
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function Example() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Form Example</CardTitle>
      </CardHeader>
      <CardContent>
        <Input placeholder="Enter text..." />
        <Button variant="default" size="sm">
          Submit
        </Button>
      </CardContent>
    </Card>
  );
}
```

### 5. Data Fetching with TanStack Query

Efficient data fetching and caching:

```tsx
'use client';
import { useQuery } from '@tanstack/react-query';

export default function UserProfile() {
  const {
    data: user,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['user'],
    queryFn: () => fetch('/api/user').then(res => res.json()),
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading user</div>;

  return <div>Welcome, {user.name}!</div>;
}
```

### 6. Internationalization

Multi-language support with React Intl:

```tsx
import { useIntl } from 'react-intl';

export default function WelcomeMessage() {
  const intl = useIntl();

  return (
    <h1>
      {intl.formatMessage({
        id: 'welcome.title',
        defaultMessage: 'Welcome to luca hub',
      })}
    </h1>
  );
}
```

### 7. Theme Management

Dark/light mode support:

```tsx
'use client';
import { useTheme } from 'next-themes';

export default function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
      Toggle theme
    </button>
  );
}
```

## File Naming Conventions

- **Components**: `kebab-case.tsx` (e.g., `user-profile.tsx`)
- **Pages**: `page.tsx`, `layout.tsx`, `loading.tsx`, `error.tsx`
- **No** `.react.tsx` extensions
- **No** separate `.styled.ts` files (use Tailwind classes)

## Import Patterns

Use absolute imports with `@/` aliases:

```tsx
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useGetMe } from '@/hooks/use-get-me';
import { API_ENDPOINTS } from '@/constants/api';
```

## Common Development Patterns

### Creating a New Page

1. **Create page file**: `src/app/(logged-in)/my-feature/page.tsx`
2. **Use Server Component by default**:

```tsx
export default function MyFeaturePage() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">My Feature</h1>
      <p>Server-rendered content</p>
    </div>
  );
}
```

### Adding Interactive Components

1. **Create client component**:
   `src/components/my-feature/interactive-widget.tsx`
2. **Mark with 'use client'**:

```tsx
'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';

export function InteractiveWidget() {
  const [count, setCount] = useState(0);

  return (
    <div className="space-y-4">
      <p>Count: {count}</p>
      <Button onClick={() => setCount(count + 1)}>Increment</Button>
    </div>
  );
}
```

### Form Handling with Validation

```tsx
'use client';
import { useState } from 'react';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email'),
});

export function UserForm() {
  const [formData, setFormData] = useState({ name: '', email: '' });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const result = schema.safeParse(formData);

    if (!result.success) {
      const fieldErrors: Record<string, string> = {};
      result.error.errors.forEach(error => {
        if (error.path[0]) {
          fieldErrors[error.path[0] as string] = error.message;
        }
      });
      setErrors(fieldErrors);
      return;
    }

    // Handle valid form submission
    console.log('Valid data:', result.data);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Input
          placeholder="Name"
          value={formData.name}
          onChange={e =>
            setFormData(prev => ({ ...prev, name: e.target.value }))
          }
        />
        {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
      </div>
      <div>
        <Input
          type="email"
          placeholder="Email"
          value={formData.email}
          onChange={e =>
            setFormData(prev => ({ ...prev, email: e.target.value }))
          }
        />
        {errors.email && <p className="text-red-500 text-sm">{errors.email}</p>}
      </div>
      <Button type="submit">Submit</Button>
    </form>
  );
}
```

## API Integration

### Creating API Routes

API routes go in `src/app/api/` directory:

```tsx
// src/app/api/users/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Fetch data from external API or database
    const users = await fetchUsers();
    return NextResponse.json(users);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const newUser = await createUser(body);
    return NextResponse.json(newUser, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
```

### Using API Routes with TanStack Query

```tsx
'use client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export function UserList() {
  const queryClient = useQueryClient();

  const { data: users, isLoading } = useQuery({
    queryKey: ['users'],
    queryFn: () => fetch('/api/users').then(res => res.json()),
  });

  const createUserMutation = useMutation({
    mutationFn: (userData: any) =>
      fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      }).then(res => res.json()),
    onSuccess: () => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  if (isLoading) return <div>Loading users...</div>;

  return (
    <div>
      {users?.map((user: any) => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  );
}
```

## Troubleshooting

### Common Issues

**Hydration Errors:**

- Ensure server and client render the same content
- Use `suppressHydrationWarning` for dynamic content
- Check for nested button elements

**Import Errors:**

- Verify `@/` alias paths in `tsconfig.json`
- Check component exports are named correctly
- Ensure client components are marked with `'use client'`

**Styling Issues:**

- Use `cn()` utility for conditional classes
- Check Tailwind class names are correct
- Verify CSS custom properties for theming

**State Management:**

- Use TanStack Query for server state
- Use React Context for global client state
- Avoid mixing server and client state

## Benefits

- **Modern Stack**: Latest Next.js, React, and TypeScript features
- **Performance**: Server-side rendering and optimized bundling
- **Accessibility**: Built-in accessibility with Radix UI components
- **Developer Experience**: Hot reloading, TypeScript, and excellent tooling
- **Scalability**: Modular architecture and efficient data fetching
- **Internationalization**: Built-in support for multiple languages
- **AI Integration**: Ready-to-use AI features with streaming support
