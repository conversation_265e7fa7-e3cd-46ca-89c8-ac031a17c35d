'use client';

import { QueryProvider } from './query-provider';
import { IntlProvider } from './intl-provider';
import { ThemeProvider } from './theme-provider';

interface ProvidersProps {
  children: React.ReactNode;
}

/**
 * Root providers component that wraps all application providers.
 * Add new providers here to maintain a clean provider hierarchy.
 */
export function Providers({ children }: ProvidersProps) {
  return (
    <QueryProvider>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <IntlProvider>{children}</IntlProvider>
      </ThemeProvider>
    </QueryProvider>
  );
}
